# Deployment Guide

This guide explains how to deploy the prompt-evals application using Docker.

## Architecture

The deployment uses a multi-stage Docker build that:

1. **Frontend Build Stage**: Builds the React frontend using Vite
2. **Production Stage**: Sets up nginx and Node.js backend in a single container

The nginx server:
- Serves the built frontend files from `/`
- Proxies `/api/*` requests to the backend server running on port 3001
- Handles proper caching, compression, and security headers

## Quick Start

### Using Docker Compose (Recommended)

```bash
# Build and start the application
docker-compose up --build

# Run in detached mode
docker-compose up -d --build

# View logs
docker-compose logs -f

# Stop the application
docker-compose down
```

### Using Docker directly

```bash
# Build the image
docker build -t prompt-evals .

# Run the container
docker run -p 80:80 -v $(pwd)/server/data:/app/server/data prompt-evals

# Run in detached mode
docker run -d -p 80:80 -v $(pwd)/server/data:/app/server/data --name prompt-evals-app prompt-evals
```

## Access

Once deployed, the application will be available at:
- **Frontend**: http://localhost
- **API Health Check**: http://localhost/api/health
- **API Endpoints**: http://localhost/api/*

## Environment Variables

Make sure your `.env` file contains the required environment variables:

```env
GEMINI_API_KEY=your_api_key_here
```

## Data Persistence

The deployment mounts the `server/data` directory to persist:
- `evaluations.json`
- `prompts.json`

## Production Considerations

### Security
- Update the Content Security Policy in `nginx.conf` for your domain
- Use HTTPS in production (add SSL certificates and update nginx config)
- Set proper CORS origins in the backend

### Scaling
- For high traffic, consider using a reverse proxy like Traefik or HAProxy
- Use a proper database instead of JSON files
- Consider separating frontend and backend into different containers

### Monitoring
- The container includes health checks for monitoring
- Logs are available via `docker-compose logs` or `docker logs`

## Troubleshooting

### Container won't start
```bash
# Check logs
docker-compose logs

# Check if ports are available
netstat -tulpn | grep :80
```

### API requests failing
```bash
# Check if backend is running
docker-compose exec prompt-evals ps aux | grep node

# Test API directly
curl http://localhost/api/health
```

### Frontend not loading
```bash
# Check nginx status
docker-compose exec prompt-evals ps aux | grep nginx

# Check nginx logs
docker-compose exec prompt-evals cat /var/log/nginx/error.log
```
